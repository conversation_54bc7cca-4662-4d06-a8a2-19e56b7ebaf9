import 'dart:ui';

import 'agent.dart';

class Broker {
  String name;
  int sales;
  double totalSalesRevenue;
  double totalCommission;
  String imageUrl;
  String contact;
  String email;
  List<Agent> agents;
  Color color;

  Broker({
    required this.name,
    required this.sales,
    required this.imageUrl,
    required this.contact,
    required this.email,
    required this.agents,
    required this.totalSalesRevenue,
    required this.totalCommission,
    required this.color,
  });

  factory Broker.fromJson(Map<String, dynamic> json) {
    return Broker(
      name: json['name'] as String,
      sales: json['sales'] as int,
      imageUrl: json['imageUrl'] as String,
      contact: json['contact'] as String,
      email: json['email'] as String,
      agents: json['agents'] as List<Agent>,
      totalSalesRevenue: json['amount'] as double,
      totalCommission: json['commission'] as double,
      color: json['color'] as Color,
    );
  }
}
