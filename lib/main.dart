import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:neorevv/config/constants.dart';
import '/screens/dashboard/dashboard_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/sales/sales_review_doc_screen.dart';
import 'theme/app_theme.dart';
import 'screens/broker/register_broker_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'NeoRevv Dashboard',
      theme: ThemeData(
        fontFamily: fontFamily,
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppTheme.primaryColor,
          brightness: Brightness.light,
        ),

        scaffoldBackgroundColor: AppTheme.scaffoldBgColor,
      ),
      home: DashboardScreen(),
      routes: {
        '/dashboard': (context) => const DashboardScreen(),
        '/register-broker': (context) => RegisterBrokerScreen(),
        '/sale-review-doc': (context) => SalesReviewDocScreen(),
      },
    );
  }
}
