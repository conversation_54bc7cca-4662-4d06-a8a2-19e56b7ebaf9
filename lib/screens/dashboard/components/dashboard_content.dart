import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '/config/app_strings.dart';
import '../../../enum/user_role.dart';
import '../../../models/user.dart';
import '/config/constants.dart';
import '/config/responsive.dart';
import '/screens/dashboard/components/info_card_widget.dart';
import '/screens/dashboard/components/brokers_table.dart';
import '/screens/dashboard/components/commission_card.dart';
import '/screens/dashboard/components/sales_by_brokers_card.dart';

import '../../../theme/app_theme.dart';
import '../../../config/json_consts.dart';

class DashboardContent extends HookWidget {
  DashboardContent({Key? key}) : super(key: key);

  final User user = User(
    name: "Nabil",
    email: "<EMAIL>",
    phone: "9876543210",
    image: "$imageAssetpath/profile.png",
    role: UserRole.platformOwner,
  );

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;
    final isDesktop = Responsive.isDesktop(context);
    final isTablet = Responsive.isTablet(context);
    final isMobile = Responsive.isMobile(context);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 5,
          child: Column(
            children: [
              // infocardWidget(context),
              // SizedBox(height: defaultPadding),
              // BrokersTable(),
              SizedBox(height: defaultPadding),
              if (Responsive.isMobile(context))
                SizedBox(height: defaultPadding),

              if (isMobile || isTablet) ...[CommissionCard()],
              // if (Responsive.isMobile(context) ||
              //     _size.width < commissionCardBreakPoint)
              //   Column(
              //     children: [
              //     ],
              //   ),
            ],
          ),
        ),
        if (!Responsive.isMobile(context)) SizedBox(width: defaultPadding),

        // On Mobile means if the screen is less than 850 we don't want to show it
        // if (!Responsive.isMobile(context) &&
        //     _size.width > commissionCardBreakPoint)
        if (isDesktop) ...[CommissionCard()],
      ],
    );
  }

  BoxDecoration _buildBoxDecoration() {
    return BoxDecoration(
      color: AppTheme.commissionCardColor,
      borderRadius: BorderRadius.circular(15),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.26),
          blurRadius: 5,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  Widget infocardWidget(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return Responsive(
      mobile: InfoCardGridView(
        crossAxisCount: 2,
        childAspectRatio: size.width < 700 && size.width > 350 ? 1.8 : 2.5,
      ),
      tablet: InfoCardGridView(
        crossAxisCount: 2,
        childAspectRatio: size.width < tabletBreakpoint && size.width > 700
            ? 3
            : 2.2,
      ),
      desktop: InfoCardGridView(
        childAspectRatio: size.width < desktopBreakpoint ? 1.8 : 2.2,
      ),
    );
  }
}

class InfoCardGridView extends StatelessWidget {
  const InfoCardGridView({
    Key? key,
    this.crossAxisCount = 4,
    this.childAspectRatio = 1,
  }) : super(key: key);

  final int crossAxisCount;
  final double childAspectRatio;

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;

    return GridView.builder(
      physics: NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: infoCards.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: defaultPadding,
        mainAxisSpacing: defaultPadding,
        childAspectRatio: childAspectRatio,
      ),
      itemBuilder: (context, index) => InfoCardWidget(
        title: infoCards[index].title,
        value: infoCards[index].value,
        icon: infoCards[index].assetImage,
        iconColor: infoCards[index].iconColor,
        subtitle: infoCards[index].subtitle,
        additionalInfo: infoCards[index].additionalInfo,
      ),
    );
  }
}

class Footer extends StatelessWidget {
  const Footer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isMobile = Responsive.isMobile(context);
    final bool isSmallMobile = Responsive.isSmallMobile(context);

    return Container(
      padding: EdgeInsets.symmetric(
        vertical: isMobile ? defaultPadding / 2 : defaultPadding,
        horizontal: defaultPadding,
      ),
      child: isSmallMobile
          ? _buildMobileFooter(context)
          : _buildDesktopFooter(context, isMobile),
    );
  }

  Widget _buildDesktopFooter(BuildContext context, bool isMobile) {
    final double spacing = isMobile ? 2.0 : 4.0;
    final double fontSize = isMobile ? 10.0 : 12.0;

    // Always use Wrap to prevent overflow
    return Wrap(
      alignment: WrapAlignment.center,
      crossAxisAlignment: WrapCrossAlignment.center,
      spacing: spacing,
      runSpacing: 4.0,
      children: [
        Text(
          copyright,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(fontSize: fontSize),
          textAlign: TextAlign.center,
        ),
        _buildFooterButton(homeFooterLabel, fontSize),
        _buildFooterButton(privacyPolicy, fontSize),
        _buildFooterButton(termsAndConditions, fontSize),
      ],
    );
  }

  Widget _buildFooterButton(String text, double fontSize) {
    return TextButton(
      onPressed: () {},
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: fontSize,
          color: AppTheme.primaryTextColor.withValues(alpha: 0.5),
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  Widget _buildMobileFooter(BuildContext context) {
    return Column(
      children: [
        Text(
          copyright,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 10),
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          maxLines: 2,
        ),
        SizedBox(height: 8),
        Wrap(
          alignment: WrapAlignment.center,
          spacing: 4,
          runSpacing: 4,
          children: [
            _buildFooterButton(homeFooterLabel, 10),
            _buildFooterButton(privacyPolicy, 10),
            _buildFooterButton(termsAndConditions, 10),
          ],
        ),
      ],
    );
  }
}
