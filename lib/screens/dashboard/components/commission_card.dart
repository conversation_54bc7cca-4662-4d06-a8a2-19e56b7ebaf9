import 'package:flutter/material.dart';
import '../../../config/app_strings.dart';
import '../../../models/agent.dart';
import '../../../models/broker.dart';
import '../../../theme/app_theme.dart';
import '/config/constants.dart';
import '/config/responsive.dart';
import '../../../theme/app_fonts.dart';
import '../../../components/common/dropdown_chip.dart';
import '../../../config/json_consts.dart';
import 'sales_by_brokers_card.dart';
import 'view_more_button.dart';

class CommissionCard extends StatefulWidget {
  const CommissionCard({super.key});

  @override
  State<CommissionCard> createState() => _CommissionCardState();
}

class _CommissionCardState extends State<CommissionCard>
    with SingleTickerProviderStateMixin {
  Broker? selectedBroker;
  bool showBrokers = true; // true for Broker, false for Agent
  late TabController _tabController;
  List<Broker> topTenBrokers = [];
  List<Agent> topTenAgents = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    final brokers = [...brokersListJson];
    brokers.sort((a, b) => b.totalCommission.compareTo(a.totalCommission));
    topTenBrokers = brokers.take(10).toList();
    final agents = [...agentListJson];
    agents.sort((a, b) => b.amount.compareTo(a.amount));
    topTenAgents = agents.take(10).toList();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    final isSmallView =
        !Responsive.isMobile(context) &&
        size.width < 1440 &&
        size.width > commissionCardBreakPoint;

    return ClipRRect(
      borderRadius: BorderRadius.circular(15),
      child: Container(
        constraints: BoxConstraints(maxHeight: size.height),
        width: Responsive.isDesktop(context) ? size.width * 0.2 : size.width,
        decoration: _buildBoxDecoration(),
        child: Responsive.isTablet(context)
            ? Container(
                height: size.height / 1.6,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildHeader(isSmallView, size),
                          _buildTabRow(size),

                          const SizedBox(height: defaultPadding),
                          _buildPerformersList(isSmallView),
                          // const SizedBox(height: defaultPadding),

                          // const SizedBox(height: defaultPadding / 1.5),
                        ],
                      ),
                    ),

                    Expanded(
                      child: SalesByBrokersCard(
                        brokersList: showBrokers ? topTenBrokers : [],
                        agentsList: showBrokers ? [] : topTenAgents,
                      ),
                    ),
                  ],
                ),
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(isSmallView, size),
                  _buildTabRow(size),

                  const SizedBox(height: defaultPadding),
                  _buildPerformersList(isSmallView),
                  const SizedBox(height: defaultPadding),

                  SalesByBrokersCard(
                    brokersList: showBrokers ? topTenBrokers : [],
                    agentsList: showBrokers ? [] : topTenAgents,
                  ),
                  const SizedBox(height: defaultPadding / 1.5),
                ],
              ),
      ),
    );
  }

  BoxDecoration _buildBoxDecoration() {
    return BoxDecoration(
      color: AppTheme.commissionCardColor,
      borderRadius: BorderRadius.circular(15),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.26),
          blurRadius: 5,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  Widget _buildHeader(bool isSmallView, Size size) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: defaultPadding,
        horizontal: defaultPadding,
      ),
      color: AppTheme.commissionCardDarkColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Top Performers',
            style: AppFonts.semiBoldTextStyle(
              size.width < 1330 && size.width > 1200 ? 16 : 18,
              color: Colors.white,
            ),
          ),
          // size.width < 1330 && size.width > 1200
          //     ? Container()
          //     :
          _buildMonthSelection(size),
        ],
      ),
    );
  }

  Container _buildMonthSelection(Size size) {
    bool restrictSize = size.width < 1330 && size.width > 1200;
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: restrictSize ? 8 : 12,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: AppTheme.commissionDropDownBgColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            size.width < 1570 && size.width > 1200 ? 'Month' : 'Select Month',
            style: AppFonts.mediumTextStyle(
              restrictSize ? 12 : 14,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 4),
          Icon(
            Icons.keyboard_arrow_down,
            color: Colors.white,
            size: restrictSize ? 14 : 16,
          ),
        ],
      ),
    );
  }

  Widget _buildTabRow(Size size) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(
        defaultPadding,
        0,
        defaultPadding,
        defaultPadding,
      ),
      decoration: BoxDecoration(color: AppTheme.commissionCardDarkColor),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(50),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Container(
            decoration: BoxDecoration(
              color: AppTheme.commissionCardColor,
              borderRadius: BorderRadius.circular(50),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTab('Broker', showBrokers, () {
                  setState(() {
                    showBrokers = true;
                  });
                }),
                _buildTab('Agent', !showBrokers, () {
                  setState(() {
                    showBrokers = false;
                  });
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTab(String text, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.commissionDropDownBgColor
              : AppTheme.commissionCardColor,
          borderRadius: isSelected
              ? BorderRadius.circular(20)
              : BorderRadius.only(
                  topRight: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
        ),
        child: Text(
          text,
          style: AppFonts.mediumTextStyle(14, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildPerformersList(bool isSmallView) {
    return Expanded(
      child: Container(
        color: AppTheme.commissionCardColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              'Commission',
              textAlign: TextAlign.center,
              style: AppFonts.semiBoldTextStyle(16, color: Colors.white),
            ),
            const SizedBox(height: defaultPadding),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
                itemCount: showBrokers
                    ? topTenBrokers.length
                    : topTenAgents.length,
                itemBuilder: (context, index) {
                  final bgColor = index % 2 == 0
                      ? AppTheme.commissionCardColor
                      : AppTheme.commissionCardDarkColor;
                  if (showBrokers) {
                    final broker = topTenBrokers[index];
                    return _buildBrokerPerformerItem(
                      broker,
                      isSmallView,
                      bgColor,
                    );
                  } else {
                    final agent = topTenAgents[index];
                    return _buildAgentPerformerItem(
                      agent,
                      isSmallView,
                      bgColor,
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBrokerPerformerItem(
    Broker broker,
    bool isSmallView,
    Color bgColor,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 5),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 1.3,
          vertical: defaultPadding / 2.5,
        ),
        child: Row(
          children: [
            _userAvatar(),
            const SizedBox(width: 12),
            _nameSalesCountWidget(broker.name, broker.agents.length, false, ''),
            _earningsWidget(broker.totalCommission),
          ],
        ),
      ),
    );
  }

  Widget _userAvatar() {
    return CircleAvatar(
      radius: 18,
      backgroundColor: Colors.white.withOpacity(0.2),
      child: Icon(Icons.person, color: Colors.white, size: 18),
    );
  }

  Widget _buildAgentPerformerItem(
    Agent agent,
    bool isSmallView,
    Color bgColor,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 5),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 1.3,
          vertical: defaultPadding / 2.5,
        ),
        child: Row(
          children: [
            _userAvatar(),
            const SizedBox(width: 12),
            _nameSalesCountWidget(
              agent.name,
              agent.agents.length,
              true,
              agent.relatedBroker,
            ),
            SizedBox(width: showBrokers ? 0 : 12),
            _earningsWidget(agent.amount),
          ],
        ),
      ),
    );
  }

  Widget _nameSalesCountWidget(
    String name,
    int agentsCount,
    bool isAgent,
    String relatedBroker,
  ) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            name,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: AppFonts.semiBoldTextStyle(13, color: Colors.white),
          ),
          SizedBox(height: 3),
          Text(
            '$agentsCount Agents',
            style: AppFonts.regularTextStyle(
              11,
              color: AppTheme.commissionSalesTextColor,
            ),
          ),

          if (isAgent) ...[
            Divider(color: AppTheme.commissionSalesTextColor),
            Text(
              'Related Broker',
              style: AppFonts.regularTextStyle(
                11,
                color: AppTheme.commissionSalesTextColor,
              ),
            ),
            SizedBox(height: 3),
            Text(
              relatedBroker,
              style: AppFonts.semiBoldTextStyle(13, color: AppTheme.white),
            ),
          ],
        ],
      ),
    );
  }

  Widget _earningsWidget(double totalCommission) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '\$${totalCommission.toInt()}',
          style: AppFonts.semiBoldTextStyle(14, color: Colors.white),
        ),
        SizedBox(height: 3),
        Text(
          'Revenue Earned',
          maxLines: 2,
          style: AppFonts.mediumTextStyle(
            10,
            color: AppTheme.commissionSalesTextColor,
          ),
        ),
      ],
    );
  }
}
