import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:neorevv/models/sales_data.dart';
import '../../../components/common/dropdown_chip.dart';
import '../../../config/app_strings.dart';
import '../../../config/constants.dart';
import '../../../models/agent.dart';
import '../../../models/broker.dart';
import '../../../theme/app_theme.dart';
import '../../../theme/app_fonts.dart';
import '../../../config/responsive.dart';

class SalesByBrokersCard extends StatefulWidget {
  final List<Broker> brokersList;
  final List<Agent> agentsList;
  const SalesByBrokersCard({
    super.key,
    required this.brokersList,
    required this.agentsList,
  });

  @override
  State<SalesByBrokersCard> createState() => _SalesByBrokersCardState();
}

class _SalesByBrokersCardState extends State<SalesByBrokersCard> {
  late Broker selectedBroker;
  int _currentAgentIndex = 0;
  int touchedIndex = -1;
  int chartSectionsCount = 0;
  bool isBrokerView = false;

  @override
  void initState() {
    super.initState();
    // Default to first broker
    // if (brokersListJson.isNotEmpty) {
    //   selectedBroker = brokersListJson[0];
    // }

    isBrokerView = widget.brokersList.isNotEmpty;
    chartSectionsCount = isBrokerView
        ? widget.brokersList.length
        : widget.agentsList.length;
  }

  @override
  Widget build(BuildContext context) {
    final bool isMobile = Responsive.isMobile(context);
    final bool isTablet = Responsive.isTablet(context);
    final bool isDesktop = Responsive.isDesktop(context);
    final Size size = MediaQuery.of(context).size;
    final isSmallView = Responsive.isSmallMobile(context);
    double maxHeight = isTablet ? size.height * 0.4 : 500;

    return Expanded(
      child: Container(
        // constraints: BoxConstraints(
        //   maxHeight: maxHeight, // isSmallView ? 500 : 700,
        //   minHeight: maxHeight, // isSmallView ? 400 : 400,
        // ),
        margin: isTablet
            ? EdgeInsets.fromLTRB(
                0,
                defaultPadding / 1.5,
                defaultPadding / 1.5,
                defaultPadding / 1.5,
              )
            : const EdgeInsets.symmetric(horizontal: defaultPadding / 1.5),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),

          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),

        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  'Sales',
                  textAlign: TextAlign.center,
                  style: AppFonts.semiBoldTextStyle(
                    16,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
              ),
              Expanded(
                child: Responsive(
                  smallMobile: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Pie Chart
                      _pieChart(size),
                      // Legend
                      _pieChartLegend(context),
                      // Spacer(),
                    ],
                  ),
                  mobile: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Pie Chart
                      _pieChart(size),
                      // Legend
                      _pieChartLegend(context),
                    ],
                  ),
                  tablet: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Pie Chart
                      _pieChart(size),
                      // Legend
                      _pieChartLegend(context),
                      // Spacer(),
                    ],
                  ),
                  desktop: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // _buildHeader(isSmallView, size),
                      // const SizedBox(height: defaultPadding),
                      // _buildSelectionRow(context, isSmallView),
                      // Donut chart
                      // _donutChart(isMobile),

                      // Agent stats row
                      // _agentStatsRow(isMobile),
                      // const Divider(
                      //   color: AppTheme.textFieldBorder,
                      //   indent: 0,
                      //   endIndent: 0,
                      //   thickness: 1,
                      //   height: 1,
                      // ),
                      // // View More
                      // GestureDetector(
                      //   onTap: () {},
                      //   child: Padding(
                      //     padding: const EdgeInsets.only(right: defaultPadding, top: 10),
                      //     child: Row(
                      //       mainAxisAlignment: MainAxisAlignment.end,
                      //       children: [
                      //         Icon(
                      //           Icons.arrow_forward_ios,
                      //           color: AppTheme.black,
                      //           size: isMobile ? 12 : 14,
                      //         ),
                      //         const SizedBox(width: 5),
                      //         Text(
                      //           viewMore,
                      //           style: AppFonts.mediumTextStyle(
                      //             isMobile ? 12 : 14,
                      //             color: AppTheme.black,
                      //           ),
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      // ),

                      // Pie Chart
                      _pieChart(size),
                      // Legend
                      _pieChartLegend(context),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _pieChart(Size size) {
    return Expanded(
      flex: 2,
      child: Container(
        // height: isSmallMobile ? 400 : 700,
        // width: 400,
        child: Align(
          alignment: Alignment.center,
          child: Stack(
            children: [
              _buildPieChart(size),
              if (touchedIndex != -1) _chartBadge(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPieChart(Size size) {
    return PieChart(
      PieChartData(
        pieTouchData: PieTouchData(
          touchCallback: (FlTouchEvent event, pieTouchResponse) {
            //TO DO: handle navigation to selected sales info
            setState(() {
              if (pieTouchResponse != null &&
                  pieTouchResponse.touchedSection!.touchedSectionIndex !=
                      touchedIndex) {
                if (!event.isInterestedForInteractions ||
                    pieTouchResponse == null ||
                    pieTouchResponse.touchedSection == null) {
                  touchedIndex = -1;
                  return;
                }
                touchedIndex =
                    pieTouchResponse.touchedSection!.touchedSectionIndex;
              }
            });
          },
        ),
        borderData: FlBorderData(show: true),
        sectionsSpace: 1.5,
        centerSpaceRadius: 0,
        sections: _showingSections(size),
      ),
    );
  }

  List<PieChartSectionData> _showingSections(Size size) {
    final isTablet = Responsive.isTablet(context);
    return List.generate(chartSectionsCount, (i) {
      final isTouched = i == touchedIndex;
      // final radius = isTouched
      //     ? size.width < 300
      //           ? 80.0
      //           : 90.0
      //     : size.width < 300
      //     ? 70.0
      //     : 80.0
      final radius = isTouched
          ? isTablet
                ? 110.0
                : 70.0
          : isTablet
          ? 100.0
          : 65.0;

      return PieChartSectionData(
        showTitle: false,
        borderSide: BorderSide.none,
        color: isBrokerView
            ? widget.brokersList[i].color
            : widget.agentsList[i].color,
        value: isBrokerView
            ? widget.brokersList[i].totalCommission.toDouble()
            : widget.agentsList[i].amount.toDouble(),
        title: '', // Remove title text from pie chart
        radius: radius,
        titleStyle: AppFonts.semiBoldTextStyle(0, color: Colors.white),
      );
    });
  }

  Positioned _chartBadge() {
    final color = isBrokerView
        ? widget.brokersList[touchedIndex].color
        : widget.agentsList.isNotEmpty
        ? widget.agentsList[touchedIndex].color
        : Colors.transparent;

    final name = isBrokerView
        ? widget.brokersList[touchedIndex].name
        : widget.agentsList.isNotEmpty
        ? widget.agentsList[touchedIndex].name
        : '';
    final sales = isBrokerView
        ? widget.brokersList[touchedIndex].sales
        : widget.agentsList.isNotEmpty
        ? widget.agentsList[touchedIndex].sales
        : 0;
    return Positioned(
      top: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              name,
              style: AppFonts.semiBoldTextStyle(11, color: Colors.white),
            ),
            Text(
              '$sales Sales',
              style: AppFonts.semiBoldTextStyle(9, color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _pieChartLegend(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final isMobile = Responsive.isMobile(context);
    final isTablet = Responsive.isTablet(context);
    final isDesktop = Responsive.isDesktop(context);
    final isSmallMobile = Responsive.isSmallMobile(context);
    final crossAxisCount = size.width < 300
        ? 1
        : isSmallMobile
        ? 1
        : isTablet
        ? 2
        : 2;
    return Expanded(
      flex: isTablet || (size.width < mobileBreakpoint && size.width > 650)
          ? 2
          : 3,
      child: Container(
        margin: const EdgeInsets.fromLTRB(
          defaultMargin,
          defaultMargin / 2,
          defaultMargin,
          defaultMargin,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.salesLegendBorderColor, width: 1),
          borderRadius: BorderRadius.circular(12),
        ),
        // padding: const EdgeInsets.all(defaultPadding / 2),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              childAspectRatio: isMobile
                  ? 6
                  : size.width > 1200 && size.width < 1800 && !isMobile
                  ? isTablet
                        ? 4
                        : 3.3
                  : 4.8,
            ),
            itemCount: chartSectionsCount,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  // Navigator.push(
                  //   context,
                  //   MaterialPageRoute(
                  //     builder: (context) =>
                  //         SalesDetailScreen(salesPerson: salesData[index]),
                  //   ),
                  // );
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: touchedIndex == index
                        ? isBrokerView
                              ? widget.brokersList[index].color.withValues(
                                  alpha: 0.1,
                                )
                              : widget.agentsList.isNotEmpty
                              ? widget.agentsList[index].color.withValues(
                                  alpha: 0.1,
                                )
                              : Colors.transparent
                        : Colors.transparent,
                    border: Border(
                      bottom: BorderSide(
                        color: AppTheme.salesLegendBorderColor,
                        width: 1,
                      ),
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,

                      children: [
                        // color square
                        Container(
                          width: 10,
                          height: 10,
                          decoration: BoxDecoration(
                            color: isBrokerView
                                ? widget.brokersList[index].color
                                : widget.agentsList.isNotEmpty
                                ? widget.agentsList[index].color
                                : Colors.transparent,

                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                        SizedBox(width: 8),
                        _legendUserInfo(index, isDesktop || isTablet),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Expanded _legendUserInfo(int index, bool isDesktop) {
    final color = isBrokerView
        ? widget.brokersList[index].color
        : widget.agentsList.isNotEmpty
        ? widget.agentsList[touchedIndex].color
        : Colors.transparent;

    final name = isBrokerView
        ? widget.brokersList[index].name
        : widget.agentsList.isNotEmpty
        ? widget.agentsList[touchedIndex].name
        : '';
    final sales = isBrokerView
        ? widget.brokersList[index].sales
        : widget.agentsList.isNotEmpty
        ? widget.agentsList[touchedIndex].sales
        : 0;
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: Text(
              name,
              style: AppFonts.mediumTextStyle(
                isDesktop ? 10 : 8,
                color: AppTheme.primaryTextColor,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Flexible(
            child: Text(
              '$sales Sales',
              style: AppFonts.mediumTextStyle(
                isDesktop ? 8 : 10,
                color: AppTheme.breadcrumbArrowColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _agentStatsRow(bool isMobile) {
    // Calculate total sales for percentage calculation
    final totalSales = selectedBroker.agents.fold(
      0,
      (sum, agent) => sum + agent.sales,
    );

    // Calculate the max index to start from
    final maxStartIndex = selectedBroker.agents.length > 2
        ? selectedBroker.agents.length - 2
        : 0;

    // Ensure current index is within bounds
    _currentAgentIndex = _currentAgentIndex.clamp(0, maxStartIndex);

    return Container(
      padding: const EdgeInsets.only(
        left: defaultPadding,
        right: defaultPadding,
        bottom: defaultPadding,
      ),
      child: Row(
        children: [
          // Left arrow
          _ArrowButton(
            icon: Icons.chevron_left,
            isMobile: isMobile,
            isEnabled: _currentAgentIndex > 0,
            onTap: _currentAgentIndex > 0
                ? () {
                    setState(() {
                      if (_currentAgentIndex > 0) {
                        _currentAgentIndex--;
                      }
                    });
                  }
                : null,
          ),
          SizedBox(width: isMobile ? 8 : 12),
          Expanded(
            child: Row(
              spacing: 5,
              children: selectedBroker.agents
                  .skip(_currentAgentIndex)
                  .take(2)
                  .map((agent) {
                    final percentage = (agent.sales / totalSales * 100)
                        .toStringAsFixed(0);
                    return Expanded(
                      child: _AgentStat(
                        color: agent.color,
                        percent: "$percentage%",
                        name: agent.name,
                        sales: agent.sales.toString(),
                        isMobile: isMobile,
                      ),
                    );
                  })
                  .toList(),
            ),
          ),
          SizedBox(width: isMobile ? 8 : 12),
          // Right arrow
          _ArrowButton(
            icon: Icons.chevron_right,
            isMobile: isMobile,
            isEnabled: _currentAgentIndex < maxStartIndex,
            onTap: _currentAgentIndex < maxStartIndex
                ? () {
                    setState(() {
                      _currentAgentIndex++;
                    });
                  }
                : null,
          ),
        ],
      ),
    );
  }

  Center _donutChart(bool isMobile) {
    return Center(
      child: Container(
        width: isMobile ? 160 : 200,
        height: isMobile ? 160 : 200,
        child: Stack(
          alignment: Alignment.center,
          children: [
            PieChart(
              PieChartData(
                sectionsSpace: 0,
                centerSpaceRadius: isMobile ? 40 : 50,
                startDegreeOffset: -90,
                sections: _getBrokerSections(isMobile),
              ),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "${selectedBroker.agents.fold(0, (sum, agent) => sum + agent.sales)}",
                  style: AppFonts.semiBoldTextStyle(
                    isMobile ? 28 : 36,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  sales,
                  style: AppFonts.regularTextStyle(
                    isMobile ? 14 : 16,
                    color: AppTheme.primaryTextColor.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _getBrokerSections(bool isMobile) {
    // Calculate total sales for percentage calculation
    final totalSales = selectedBroker.agents.fold(
      0,
      (sum, agent) => sum + agent.sales,
    );

    // Create sections for the first 4 brokers (or fewer if there are less than 4)
    final sections = <PieChartSectionData>[];

    for (int i = 0; i < selectedBroker.agents.length && i < 4; i++) {
      final agent = selectedBroker.agents[i];
      final percentage = agent.sales / totalSales * 100;

      sections.add(
        PieChartSectionData(
          color: agent.color,
          value: percentage,
          showTitle: false,
          radius: isMobile ? 12 : 16,
        ),
      );
    }

    return sections;
  }

  Widget _buildHeader(bool isSmallView, Size size) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: defaultPadding),

      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            salesByBrokers,
            style: AppFonts.semiBoldTextStyle(
              18,
              color: AppTheme.primaryTextColor,
            ),
          ),
          if (!isSmallView)
            DropdownChip(
              label: size.width < 1800 ? monthLabel : selectMonth,
              bgColor: AppTheme.scaffoldBgColor,
              textColor: AppTheme.primaryTextColor,
            ),
        ],
      ),
    );
  }

  Widget _buildSelectionRow(BuildContext context, bool isSmallView) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: DropdownChip(
              label:
                  selectedBroker?.name ??
                  (Responsive.isMobile(context) && isSmallView
                      ? broker1Label
                      : selectBroker1),
              expandText: true,
              bgColor: AppTheme.scaffoldBgColor,
              textColor: AppTheme.primaryTextColor,
              isBrokerSelector: true,
              onBrokerSelected: (broker) {
                setState(() {
                  selectedBroker = broker;
                });
              },
            ),
          ),
          // if (!Responsive.isMobile(context) && isSmallView)
          //   const SizedBox(width: 8),
          // if (!Responsive.isMobile(context) && isSmallView)
          //   Expanded(
          //     child: DropdownChip(
          //       label: monthLabel,
          //       expandText: true,
          //       bgColor: AppTheme.scaffoldBgColor,
          //       textColor: AppTheme.primaryTextColor,
          //     ),
          //   ),
        ],
      ),
    );
  }
}

// Arrow button widget
class _ArrowButton extends StatelessWidget {
  final IconData icon;
  final bool isMobile;
  final bool isEnabled;
  final VoidCallback? onTap;

  const _ArrowButton({
    required this.icon,
    required this.isMobile,
    required this.isEnabled,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(isMobile ? 6 : 8),
        decoration: BoxDecoration(
          color: AppTheme.greyRoundBg,
          shape: BoxShape.circle,
          border: Border.all(
            color: isEnabled ? Colors.grey.shade200 : Colors.grey.shade100,
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          size: isMobile ? 14 : 16,
          color: isEnabled
              ? AppTheme.primaryTextColor
              : AppTheme.primaryTextColor.withValues(alpha: 0.3),
        ),
      ),
    );
  }
}

// Agent stat widget
class _AgentStat extends StatelessWidget {
  final Color color;
  final String percent;
  final String name;
  final String sales;
  final bool isMobile;

  const _AgentStat({
    required this.color,
    required this.percent,
    required this.name,
    required this.sales,
    required this.isMobile,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: isMobile ? 28 : 32,
            height: isMobile ? 28 : 32,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(5),
            ),
            child: Center(
              child: Text(
                percent,
                style: AppFonts.regularTextStyle(
                  isMobile ? 10 : 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          SizedBox(width: isMobile ? 6 : 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  name,
                  style: AppFonts.semiBoldTextStyle(
                    isMobile ? 10 : 12,
                    color: AppTheme.primaryTextColor,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                const SizedBox(height: 5),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: sales,
                        style: AppFonts.semiBoldTextStyle(
                          isMobile ? 10 : 12,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                      TextSpan(
                        text: " Sales",
                        style: AppFonts.regularTextStyle(
                          isMobile ? 9 : 10,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _Badge extends StatelessWidget {
  const _Badge(this.name, {required this.size, required this.borderColor});

  final String name;
  final double size;
  final Color borderColor;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: PieChart.defaultDuration,
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        border: Border.all(color: borderColor, width: 2),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: Colors.black.withOpacity(.5),
            offset: const Offset(3, 3),
            blurRadius: 3,
          ),
        ],
      ),
      padding: EdgeInsets.all(size * .15),
      child: Center(
        child: Text(
          name.split(' ').map((word) => word[0]).join(''),
          style: TextStyle(
            fontSize: size * 0.3,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ),
    );
  }
}
